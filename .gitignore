.vscode/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# PyInstaller
#  Usually these files are written by a python script from a template
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
pytestdebug.log

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# PEP 582; used by e.g. github.com/<PERSON>-<PERSON><PERSON>nor/pyflow
__pypackages__/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Pytype
.pytype/

# Cython debug symbols
cython_debug/

# VS Code
.vscode/

# IDEs
.idea/
*.sublime-project
*.sublime-workspace

# Logs
*.log

# Environment variables
.env
.env.* 

LidarMetadataExtractionService/archive/
LidarMetadataExtractionService/mmdetection3d/.git
LidarMetadataExtractionService/mmdetection3d/.github
LidarMetadataExtractionService/mmdetection3d/data
LidarMetadataExtractionService/mmdetection3d/demo
LidarMetadataExtractionService/mmdetection3d/projects
LidarMetadataExtractionService/mmdetection3d/README.md
LidarMetadataExtractionService/mmdetection3d/LICENSE
LidarMetadataExtractionService/mmdetection3d/.dev_scripts
LidarMetadataExtractionService/mmdetection3d/.circleci
RedisStorageService/redis-data/
#!/bin/bash
# start_containers.sh

# Optional: stop script if any command fails
set -e

# Navigate to the directory containing your docker-compose.yml

echo "🚀 Building and starting Docker Compose services..."
echo "📦 Services include: ROS2, MMDetection3D, Ollama, Redis Storage, Backend, Frontend"

# Build and start containers in detached mode
docker compose up --build -d

echo "✅ Containers started successfully."
echo "🔗 Service URLs:"
echo "   - Frontend (Streamlit): http://localhost:8501"
echo "   - Backend (FastAPI): http://localhost:8000"
echo "   - Redis Storage API: http://localhost:8002"
echo "   - Redis Database: localhost:6379"
echo "   - MMDetection3D: http://localhost:8081"
echo "   - Ollama: http://localhost:11434"

# Optional: show logs
echo "📜 Displaying logs (Press Ctrl+C to exit)"
docker compose logs -f
#!/bin/bash
# stop_containers.sh

# Optional: stop script if any command fails
set -e

echo "🛑 Stopping Docker Compose services..."
echo "📦 Stopping: ROS2, MMDetection3D, Ollama, Redis Storage, Backend, Frontend"

# Stop and remove containers, networks, and default volumes
docker compose down

echo "✅ Containers stopped and removed successfully."
echo "💾 Note: Redis data is preserved in ./RedisStorageService/redis-data/"

services:
  # ==============================================================
  # ROS 2 Service (GPU)
  # ==============================================================
  ros2:
    build:
      context: ./LidarROSService
      dockerfile: Dockerfile.local
    container_name: ros2-local-data-container-2
    shm_size: '500g'
    ulimits:
      memlock: -1
      stack: 67108864
    working_dir: /ros_service
    volumes:
      - ./LidarROSService:/ros_service
      - /brain-data:/brain-data:ro
      - /data:/data:ro
    depends_on:
      - mmdetection3d
    command: bash ./start-dev.sh
  # ==============================================================
  # Lidar Metadata Extraction Service
  # ==============================================================
  mmdetection3d:
    build:
      context: ./LidarMetadataExtractionService
      dockerfile: Dockerfile
    container_name: lidar-metadata-extraction
    volumes:
      - ./LidarMetadataExtractionService/mmdetection3d:/mmdetection3d
    ports:
      - "8081:8081"
    command: >
      conda run --no-capture-output -n openmmlab
      python -m uvicorn app:app --host 0.0.0.0 --port 8081 --reload
      
  # ==============================================================
  # Ollama Service (GPU)
  # ==============================================================
  ollama:
    build:
      context: ./OllamaService
      dockerfile: Dockerfile
    container_name: ollama-gpu
    ports:
      - "11434:11434"
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - OLLAMA_MODELS=/root/.ollama/models
    volumes:
      - ollama_models:/root/.ollama/models
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]
    restart: unless-stopped

  # ==============================================================
  # Backend (FastAPI)
  # ==============================================================
  backend:
    build:
      context: ./LidarInferenceService
      dockerfile: Dockerfile
    container_name: lidar-inference-service
    depends_on:
      - ollama
      - mmdetection3d
      - ros2
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - OLLAMA_API_URL=http://ollama:11434
    volumes:
      - ./LidarInferenceService/app:/app/app
    working_dir: /app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 40s

  # ==============================================================
  # Front End (Streamlit)
  # ==============================================================
  frontend:
    build:
      context: ./StreamlitUIService
      dockerfile: Dockerfile
    ports:
      - "8501:8501"
    volumes:
      - ./StreamlitUIService:/app
    environment:
      - PYTHONUNBUFFERED=1
      - LIDAR_INFERENCE_SERVICE_URL=http://lidar-inference-service:8000
    depends_on:
      - backend
    command: >
      streamlit run app.py
      --server.port=8501
      --server.address=0.0.0.0
      --server.runOnSave=true

volumes:
  ollama_models:
  lidar_db_data:
  redis_data:

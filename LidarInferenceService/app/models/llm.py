from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr


# ------------------------
# Define Input Schema
# ------------------------
class RequestModel(BaseModel):
    prompt: str
    context: str
    model_name: Optional[str] = "llama3:8b"  # Default model
    system_prompt: Optional[str] = None  # Optional custom system prompt
    user_session: Optional[str] = None  # Optional user session ID
   
# ------------------------
# Define Output Schema
# ------------------------
class ResponseModel(BaseModel):
    response: str

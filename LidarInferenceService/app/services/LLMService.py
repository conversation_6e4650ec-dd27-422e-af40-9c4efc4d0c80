import re
from datetime import datetime
from typing import List, Optional
import subprocess
from sqlalchemy.orm import Session
from app.models.llm import <PERSON>questModel
from app.prompts.llm import PROMPT_TEMPLATE, AVAILABLE_PROMPTS
import requests
import os
from langchain_core.prompts import <PERSON>t<PERSON><PERSON>pt<PERSON>emplate
from langchain_ollama.llms import OllamaLLM
import time


class LLMService:
    def __init__(self):
        self.base_url = os.environ.get("OLLAMA_BASE_URL", "http://ollama:11434")
        self.default_model = "llama3:8b"
        # Cache for model instances to avoid recreating them
        self.model_cache = {}

    def get_model(self, model_name: str = None):
        """Get or create a model instance for the specified model name."""
        if model_name is None:
            model_name = self.default_model

        if model_name not in self.model_cache:
            self.model_cache[model_name] = OllamaLLM(model=model_name, base_url=self.base_url)
        return self.model_cache[model_name]

    def get_prompt_template(self, system_prompt: str = None):
        """Get the appropriate prompt template."""
        if system_prompt is None:
            return PROMPT_TEMPLATE

        # Check if it's a predefined prompt name
        if system_prompt in AVAILABLE_PROMPTS:
            return AVAILABLE_PROMPTS[system_prompt]

        # Otherwise, treat it as a custom prompt template
        # Ensure it has the required placeholders
        if "{input}" not in system_prompt or "{context}" not in system_prompt:
            # Add placeholders if missing
            system_prompt += "\n\nUser Input: {input}\nLiDAR Context: {context}"

        return system_prompt

    def generate_llm_response(self, prompt: str, context: str, model_name: str = None, system_prompt: str = None) -> str:
        result = self.call_generate_llm_response(input_text=prompt, context=context, model_name=model_name, system_prompt=system_prompt)
        return f"LLM response for: {result}"

    def call_generate_llm_response(self, input_text, context, model_name: str = None, system_prompt: str = None):
        try:
            model = self.get_model(model_name)
            prompt_template = self.get_prompt_template(system_prompt)
            prompt = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt | model
            response = chain.invoke({"input": input_text, "context": context})
            return response

        except Exception as e:
            error_msg = str(e)
            print(f"Error generating LLM response: {e}")

            # Check if it's a model not found error
            if "not found" in error_msg.lower() and ("model" in error_msg.lower() or model_name in error_msg):
                return f"Error generating LLM response: model '{model_name}' not found (status code: 404)"

            return f"Error generating LLM response: {e}"
    
    def stream_llm_response(self, prompt: str, context: str, model_name: str = None, system_prompt: str = None, user_session: str = None):
        """
        Yields chunks of LLM response as they are generated.
        Also stores request/response data in Redis.
        """
        start_time = time.time()
        request_id = None
        response_chunks = []
        full_response = ""

        try:
            model = self.get_model(model_name)
            prompt_template = self.get_prompt_template(system_prompt)
            prompt_template_obj = ChatPromptTemplate.from_template(prompt_template)
            chain = prompt_template_obj | model

            # Stream the response
            for chunk in chain.stream({"input": prompt, "context": context}):
                response_chunks.append(chunk)
                full_response += chunk
                yield chunk

        except Exception as e:
            error_msg = str(e)

            # Check if it's a model not found error
            if "not found" in error_msg.lower() and ("model" in error_msg.lower() or model_name in error_msg):
                error_response = f"Error generating LLM response: model '{model_name}' not found (status code: 404)"
                yield error_response

            else:
                error_response = f"Error generating LLM response: {e}"
                yield error_response
            
from typing import List
import json
from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import StreamingResponse
from app.db.schema import SessionLocal
from app.models.llm import RequestModel, ResponseModel
from app.services.LLMService import LLMService
from typing import Optional
from app.data.LidarData import latest_lidar_data, data_lock
from app.prompts.llm import AVAILABLE_PROMPTS

router = APIRouter()

# ------------------------
# Endpoint using schemas
# ------------------------

llm = LLMService()

@router.get("/available-models")
async def get_available_models():
    """Get list of available models."""
    # These should match the models available in your Ollama instance
    available_models = ["llama3:8b", "gemma3:4b"]
    return {"models": available_models}


@router.get("/available-prompts")
async def get_available_prompts():
    """Get list of available system prompts."""
    return {"prompts": list(AVAILABLE_PROMPTS.keys())}


@router.get("/prompt-content")
async def get_prompt_content(prompt_name: str = Query(..., description="Name of the system prompt")):
    """Get the full content of a specific system prompt."""
    if prompt_name not in AVAILABLE_PROMPTS:
        raise HTTPException(status_code=404, detail=f"Prompt '{prompt_name}' not found")

    return {"content": AVAILABLE_PROMPTS[prompt_name]}

@router.post("/generate-stream-with-context")
async def generate_stream(request: RequestModel):
    """Stream LLM response along with context."""
    prompt = request.prompt
    context = request.context
    model_name = request.model_name
    system_prompt = request.system_prompt

    def generate():
        yield f"**📋 Context:**\n```\n{context}\n```\n\n**🤖 Response:**\n\n"
        yield from llm.stream_llm_response(prompt, context, model_name, system_prompt)

    return StreamingResponse(generate(), media_type="text/markdown")

